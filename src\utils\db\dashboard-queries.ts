import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { format, startOfYear, subDays, subMonths, subYears } from "date-fns";
import {
  checkExchangeRateAssetsExist,
  convertAmount,
  ExchangeRateMap,
  extractPortfolioCurrencies,
  fetchExchangeRatesFromDB,
  fetchMissingExchangeRates,
  getRequiredExchangeRates,
} from "./exchange-rates";
import { hasuraQuery } from "./hasura";

// Types for dashboard data
export interface PortfolioCompositionItem {
  name: string;
  value: number;
  percentage: number;
  ticker?: string; // Only for positions view
}

export interface PortfolioComposition {
  sector: PortfolioCompositionItem[];
  industry: PortfolioCompositionItem[];
  currency: PortfolioCompositionItem[];
  country: PortfolioCompositionItem[];
  assetType: PortfolioCompositionItem[];
  positions: PortfolioCompositionItem[];
  totalValue: number;
  displayCurrency: SupportedCurrency;
}

// Types for portfolio performance data
export interface PortfolioPerformanceDataPoint {
  date: string;
  value: number;
  profitLoss: number;
  profitLossPercentage: number;
}

export interface PortfolioPerformanceData {
  data: PortfolioPerformanceDataPoint[];
  currentValue: number;
  basePortfolioValue: number; // Cost basis for current holdings
  totalProfitLoss: number;
  totalProfitLossPercentage: number;
  displayCurrency: SupportedCurrency;
  timePeriod: TimePeriod;
}

export type TimePeriod = "1W" | "1M" | "YTD" | "1Y" | "MAX";

export interface TransactionWithAssetDetails {
  id: string;
  portfolio_id: string;
  ticker: string;
  price: number;
  quantity: number;
  transaction_date: string;
  transaction_type: "BUY" | "SELL";
  asset?: {
    asset_id: number;
    ticker: string;
    name: string;
    company: string;
    sector?: { name: string };
    industry?: { name: string };
    currency?: { code: string; name: string; symbol: string };
    country?: { code: string; name: string };
    asset_type?: { name: string };
  };
  latest_price?: {
    close_price: number;
    date: string;
  };
}

// GraphQL Queries

/**
 * Get portfolio composition data with asset details and reference tables
 * This query fetches transactions with complete asset information
 */
export const GET_PORTFOLIO_COMPOSITION_DATA = `
  query GetPortfolioCompositionData($portfolioIds: [uuid!]!) {
    ptvuser_transactions(
      where: {portfolio_id: {_in: $portfolioIds}}
      order_by: {transaction_date: desc}
    ) {
      id
      portfolio_id
      ticker
      price
      quantity
      transaction_date
      transaction_type
    }
  }
`;

/**
 * Get asset details with all reference table joins
 */
export const GET_ASSETS_WITH_REFERENCES = `
  query GetAssetsWithReferences($tickers: [String!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      asset_id
      ticker
      name
      company
      sector {
        name
      }
      industry {
        name
      }
      currency {
        code
        name
        symbol
      }
      country {
        code
        name
      }
      asset_type {
        name
      }
    }
  }
`;

/**
 * Get latest asset prices for portfolio tickers
 */
export const GET_LATEST_ASSET_PRICES = `
  query GetLatestAssetPrices($tickers: [String!]!, $today: date!) {
    ptvuser_asset(where: { ticker: { _in: $tickers } }) {
      ticker
      asset_id
      latest_price: asset_prices(
        where: { date: { _lt: $today } }
        order_by: { date: desc }
        limit: 1
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

/**
 * Get historical asset prices for specific date range
 */
export const GET_HISTORICAL_ASSET_PRICES = `
  query GetHistoricalAssetPrices($tickers: [String!]!, $startDate: date!, $endDate: date!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      ticker
      asset_id
      historical_prices: asset_prices(
        where: {date: {_gte: $startDate, _lte: $endDate}}
        order_by: {date: asc}
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

/**
 * Get asset prices for specific dates (for monthly/YTD calculations)
 * @deprecated This query is no longer used as all time periods now use daily data
 */
export const GET_ASSET_PRICES_FOR_DATES = `
  query GetAssetPricesForDates($tickers: [String!]!, $dates: [date!]!) {
    ptvuser_asset(where: {ticker: {_in: $tickers}}) {
      ticker
      asset_id
      price_data: asset_prices(
        where: {date: {_in: $dates}}
        order_by: {date: asc}
      ) {
        close_price: adj_close
        date
      }
    }
  }
`;

// Utility Functions

/**
 * Find the earliest transaction date from a list of transactions
 * Prioritizes the first BUY transaction, ignoring SELL transactions that occur before any BUY
 */
export function findEarliestTransactionDate(
  transactions: TransactionWithAssetDetails[]
): string | null {
  if (transactions.length === 0) {
    return null;
  }

  // Sort transactions by date
  const sortedTransactions = transactions
    .slice()
    .sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime()
    );

  // Find the first BUY transaction
  const firstBuyTransaction = sortedTransactions.find(
    (t) => t.transaction_type === "BUY"
  );

  if (firstBuyTransaction) {
    return firstBuyTransaction.transaction_date;
  }

  // If no BUY transactions found, return the earliest transaction date
  return sortedTransactions[0].transaction_date;
}

/**
 * Calculate date range for different time periods
 */
export function calculateDateRange(
  timePeriod: TimePeriod,
  transactions?: TransactionWithAssetDetails[]
): {
  startDate: string;
  endDate: string;
} {
  const now = new Date();
  let startDate: string;
  let endDate: string;

  switch (timePeriod) {
    case "1W":
      endDate = format(subDays(now, 1), "yyyy-MM-dd"); // yesterday
      startDate = format(subDays(now, 7 + 1), "yyyy-MM-dd"); // 1 week before yesterday
      break;
    case "1M":
      endDate = format(subDays(now, 1), "yyyy-MM-dd"); // yesterday
      startDate = format(subMonths(now, 1), "yyyy-MM-dd");
      break;
    case "YTD":
      endDate = format(now, "yyyy-MM-dd"); // today
      startDate = format(startOfYear(now), "yyyy-MM-dd");
      break;
    case "1Y":
      endDate = format(now, "yyyy-MM-dd"); // today
      startDate = format(subYears(now, 1), "yyyy-MM-dd");
      break;
    case "MAX":
      endDate = format(now, "yyyy-MM-dd"); // today
      if (!transactions || transactions.length === 0) {
        startDate = format(subYears(now, 1), "yyyy-MM-dd");
      } else {
        const earliestDate = findEarliestTransactionDate(transactions);
        startDate = earliestDate || endDate;
      }
      break;
    default:
      throw new Error(`Unsupported time period: ${timePeriod}`);
  }

  return { startDate, endDate };
}

/**
 * Generate date array for monthly sampling (last day of each month)
 * @deprecated This function is no longer used as all time periods now use daily data
 */
export function generateMonthlyDates(
  startDate: string,
  endDate: string
): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  const current = new Date(start.getFullYear(), start.getMonth(), 1);

  while (current <= end) {
    // Get last day of current month
    const lastDay = new Date(current.getFullYear(), current.getMonth() + 1, 0);
    if (lastDay <= end) {
      dates.push(lastDay.toISOString().split("T")[0]);
    }
    // Move to next month
    current.setMonth(current.getMonth() + 1);
  }

  // Always include the end date if it's not already included
  const endDateStr = end.toISOString().split("T")[0];
  if (!dates.includes(endDateStr)) {
    dates.push(endDateStr);
  }

  return dates;
}

/**
 * Determine if YTD should use daily or monthly frequency
 * @deprecated This function is no longer used as all time periods now use daily data
 */
export function shouldUseMonthlyForYTD(): boolean {
  const now = new Date();
  const yearStart = new Date(now.getFullYear(), 0, 1);
  const monthsDiff =
    (now.getTime() - yearStart.getTime()) / (1000 * 60 * 60 * 24 * 30.44); // Average month length
  return monthsDiff > 3;
}

/**
 * Calculate portfolio holdings from transactions
 */
export function calculatePortfolioHoldings(
  transactions: TransactionWithAssetDetails[]
): Map<
  string,
  { quantity: number; transactions: TransactionWithAssetDetails[] }
> {
  const holdings = new Map<
    string,
    { quantity: number; transactions: TransactionWithAssetDetails[] }
  >();

  transactions.forEach((transaction) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, transactions: [] };

    const quantity =
      transaction.transaction_type === "BUY"
        ? transaction.quantity
        : -transaction.quantity;

    holdings.set(ticker, {
      quantity: existing.quantity + quantity,
      transactions: [...existing.transactions, transaction],
    });
  });

  // Filter out holdings with zero or negative quantity
  const filteredHoldings = new Map();
  holdings.forEach((value, key) => {
    if (value.quantity > 0) {
      filteredHoldings.set(key, value);
    }
  });

  return filteredHoldings;
}

/**
 * Calculate portfolio composition by different categories
 */
export function calculatePortfolioComposition(
  transactions: TransactionWithAssetDetails[],
  latestPrices: Map<string, number>,
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): PortfolioComposition {
  const holdings = calculatePortfolioHoldings(transactions);

  // Calculate current values for each holding
  const holdingValues = new Map<string, number>();
  let totalValue = 0;

  holdings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR"; // Default to EUR if no currency info

    // Calculate value in original currency
    const originalValue = holding.quantity * latestPrice;

    // Convert to display currency
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    holdingValues.set(ticker, convertedValue);
    totalValue += convertedValue;
  });

  // Group by different categories
  const sectorMap = new Map<string, number>();
  const industryMap = new Map<string, number>();
  const currencyMap = new Map<string, number>();
  const countryMap = new Map<string, number>();
  const assetTypeMap = new Map<string, number>();
  const positionsArray: PortfolioCompositionItem[] = [];

  holdings.forEach((_, ticker) => {
    const value = holdingValues.get(ticker) || 0;
    const asset = assetData.get(ticker);

    if (value === 0) return;

    // Positions
    positionsArray.push({
      name: `${asset?.name} (${ticker})`,
      ticker,
      value,
      percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
    });

    // Sector
    const sectorName = asset?.sector?.name || "Altele / Diversificat";
    sectorMap.set(sectorName, (sectorMap.get(sectorName) || 0) + value);

    // Industry
    const industryName = asset?.industry?.name || "Altele / Diversificat";
    industryMap.set(industryName, (industryMap.get(industryName) || 0) + value);

    // Currency
    const currencyName =
      asset?.currency?.name || asset?.currency?.code || "Altele";
    currencyMap.set(currencyName, (currencyMap.get(currencyName) || 0) + value);

    // Country
    const countryName = asset?.country?.name || "Diversificat";
    if (countryName === "USA" || countryName === "United States") {
      countryMap.set(
        "United States",
        (countryMap.get("United States") || 0) + value
      );
    } else {
      countryMap.set(countryName, (countryMap.get(countryName) || 0) + value);
    }

    // Asset Type
    let assetTypeName = asset?.asset_type?.name || "Altele";
    // Convert EQUITY to STOCK as requested
    if (assetTypeName === "EQUITY" || assetTypeName === "Common Stock") {
      assetTypeName = "STOCK";
    }
    assetTypeMap.set(
      assetTypeName,
      (assetTypeMap.get(assetTypeName) || 0) + value
    );
  });

  // Convert maps to arrays with percentages
  const createCompositionArray = (
    map: Map<string, number>
  ): PortfolioCompositionItem[] => {
    return Array.from(map.entries())
      .map(([name, value]) => ({
        name,
        value,
        percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
      }))
      .sort((a, b) => b.value - a.value);
  };

  return {
    sector: createCompositionArray(sectorMap),
    industry: createCompositionArray(industryMap),
    currency: createCompositionArray(currencyMap),
    country: createCompositionArray(countryMap),
    assetType: createCompositionArray(assetTypeMap),
    positions: positionsArray.sort((a, b) => b.value - a.value),
    totalValue,
    displayCurrency,
  };
}

/**
 * Fetch portfolio composition data for selected portfolios
 */
export async function getPortfolioCompositionData(
  portfolioIds: string[],
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioComposition> {
  try {
    if (portfolioIds.length === 0) {
      return {
        sector: [],
        industry: [],
        currency: [],
        country: [],
        assetType: [],
        positions: [],
        totalValue: 0,
        displayCurrency,
      };
    }

    // Fetch transactions with asset details
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];

    if (transactions.length === 0) {
      return {
        sector: [],
        industry: [],
        currency: [],
        country: [],
        assetType: [],
        positions: [],
        totalValue: 0,
        displayCurrency,
      };
    }

    // Get unique tickers for price lookup
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Fetch asset data with references and prices in parallel
    const [assetsResult, pricesResult] = await Promise.all([
      hasuraQuery<{
        ptvuser_asset: Array<{
          asset_id: number;
          ticker: string;
          name: string;
          company: string;
          sector?: { name: string };
          industry?: { name: string };
          currency?: { code: string; name: string; symbol: string };
          country?: { code: string; name: string };
          asset_type?: { name: string };
        }>;
      }>(GET_ASSETS_WITH_REFERENCES, { variables: { tickers: uniqueTickers } }),

      hasuraQuery<{
        ptvuser_asset: Array<{
          ticker: string;
          latest_price: Array<{ close_price: number; date: string }>;
        }>;
      }>(GET_LATEST_ASSET_PRICES, {
        variables: {
          tickers: uniqueTickers,
          today: format(new Date(), "yyyy-MM-dd"),
        },
      }),
    ]);

    const assetData = new Map<string, TransactionWithAssetDetails["asset"]>();
    assetsResult.ptvuser_asset?.forEach((asset) => {
      assetData.set(asset.ticker, asset);
    });

    const latestPrices = new Map<string, number>();
    pricesResult.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        latestPrices.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    // Get portfolio currencies and fetch exchange rates if needed
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);
    const requiredExchangeRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    let exchangeRates = new Map<string, number>();

    if (requiredExchangeRates.length > 0) {
      try {
        // Check which exchange rate assets exist
        const existingAssets = await checkExchangeRateAssetsExist(
          requiredExchangeRates
        );
        const missingAssets = requiredExchangeRates.filter(
          (pair) => !existingAssets.has(pair)
        );

        // Fetch missing exchange rates from EODHD API
        if (missingAssets.length > 0) {
          if (process.env.NODE_ENV === "development") {
            console.log(
              `Fetching missing exchange rates: ${missingAssets.join(", ")}`
            );
          }
          await fetchMissingExchangeRates(missingAssets);
        }

        // Fetch all exchange rates from database
        exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);
        if (process.env.NODE_ENV === "development") {
          console.log(
            `Loaded ${exchangeRates.size} exchange rates for currency conversion`
          );
          console.log("exchangeRates", exchangeRates);
        }
      } catch (error) {
        console.error("Error fetching exchange rates:", error);
        // Continue without currency conversion if exchange rates fail
      }
    }

    return calculatePortfolioComposition(
      transactions,
      latestPrices,
      assetData,
      displayCurrency,
      exchangeRates
    );
  } catch (error) {
    console.error("Error fetching portfolio composition data:", error);
    throw new Error(
      "Nu s-au putut încărca datele de compoziție ale portofoliului"
    );
  }
}

/**
 * Calculate portfolio holdings at a specific date
 */
export function calculatePortfolioHoldingsAtDate(
  transactions: TransactionWithAssetDetails[],
  targetDate: string
): Map<string, { quantity: number; costBasis: number }> {
  const holdings = new Map<string, { quantity: number; costBasis: number }>();
  const isTargetDate = targetDate === "2024-10-07";

  // Filter transactions up to target date and sort by date
  const relevantTransactions = transactions
    .filter((t) => t.transaction_date <= targetDate)
    .sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime()
    );

  if (isTargetDate && process.env.NODE_ENV === "development") {
    console.log(`\n📋 CALCULATING HOLDINGS FOR ${targetDate}:`);
    console.log(
      `📊 Total transactions up to date: ${relevantTransactions.length}`
    );
    console.log(`📈 Processing transactions in chronological order:`);
  }

  relevantTransactions.forEach((transaction, index) => {
    const ticker = transaction.ticker;
    const existing = holdings.get(ticker) || { quantity: 0, costBasis: 0 };

    if (isTargetDate && process.env.NODE_ENV === "development") {
      console.log(`\n  ${index + 1}. Transaction ${transaction.id}:`);
      console.log(`     📅 Date: ${transaction.transaction_date}`);
      console.log(`     🏷️  Ticker: ${ticker}`);
      console.log(`     📊 Type: ${transaction.transaction_type}`);
      console.log(`     💰 Price: ${transaction.price}`);
      console.log(`     📦 Quantity: ${transaction.quantity}`);
      console.log(
        `     📈 Before - Quantity: ${existing.quantity}, Cost Basis: ${existing.costBasis}`
      );
    }

    if (transaction.transaction_type === "BUY") {
      const newQuantity = existing.quantity + transaction.quantity;
      const newCostBasis =
        existing.costBasis + transaction.quantity * transaction.price;
      holdings.set(ticker, { quantity: newQuantity, costBasis: newCostBasis });

      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(
          `     ✅ BUY - After: Quantity: ${newQuantity}, Cost Basis: ${newCostBasis}`
        );
      }
    } else if (transaction.transaction_type === "SELL") {
      const newQuantity = existing.quantity - transaction.quantity;
      // Proportionally reduce cost basis
      const costBasisPerShare =
        existing.quantity > 0 ? existing.costBasis / existing.quantity : 0;
      const newCostBasis =
        existing.costBasis - transaction.quantity * costBasisPerShare;
      holdings.set(ticker, {
        quantity: Math.max(0, newQuantity),
        costBasis: Math.max(0, newCostBasis),
      });

      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(
          `     🔴 SELL - Cost Basis Per Share: ${costBasisPerShare}`
        );
        console.log(
          `     🔴 SELL - After: Quantity: ${Math.max(
            0,
            newQuantity
          )}, Cost Basis: ${Math.max(0, newCostBasis)}`
        );
      }
    }
  });

  // Filter out holdings with zero or negative quantity
  const filteredHoldings = new Map();
  holdings.forEach((value, key) => {
    if (value.quantity > 0) {
      filteredHoldings.set(key, value);
    }
  });

  if (isTargetDate && process.env.NODE_ENV === "development") {
    console.log(`\n📊 FINAL HOLDINGS FOR ${targetDate}:`);
    filteredHoldings.forEach((holding, ticker) => {
      console.log(
        `  ${ticker}: ${holding.quantity} shares, Cost Basis: ${holding.costBasis}`
      );
    });
    console.log(`📈 Total unique holdings: ${filteredHoldings.size}\n`);
  }

  return filteredHoldings;
}

/**
 * Calculate portfolio performance over time
 */
export function calculatePortfolioPerformance(
  transactions: TransactionWithAssetDetails[],
  historicalPrices: Map<string, Map<string, number>>, // ticker -> date -> price
  latestPrices: Map<string, number>, // ticker -> latest price (for current value calculation)
  assetData: Map<string, TransactionWithAssetDetails["asset"]>,
  timePeriod: TimePeriod,
  displayCurrency: SupportedCurrency = "EUR",
  exchangeRates: ExchangeRateMap = new Map()
): PortfolioPerformanceData {
  const { startDate, endDate } = calculateDateRange(timePeriod, transactions);
  if (process.env.NODE_ENV === "development") {
    console.log("startDate", startDate);
    console.log("endDate", endDate);
  }

  // Generate daily dates for all time periods
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(start);

  while (current <= end) {
    dates.push(current.toISOString().split("T")[0]);
    current.setDate(current.getDate() + 1);
  }

  const performanceData: PortfolioPerformanceDataPoint[] = [];

  // Calculate base portfolio value (cost basis for current holdings) first
  const currentHoldings = calculatePortfolioHoldings(transactions);
  let basePortfolioValue = 0;

  if (process.env.NODE_ENV === "development") {
    console.log(`\n💰 CALCULATING BASE PORTFOLIO VALUE (Cost Basis):`);
    console.log(`📊 Current Holdings:`, currentHoldings);
  }

  currentHoldings.forEach((holding, ticker) => {
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Calculate base portfolio value (cost basis for current holdings)
    let totalCostBasisForTicker = 0;
    let totalBuyQuantity = 0;

    holding.transactions.forEach((t) => {
      if (t.transaction_type === "BUY") {
        totalCostBasisForTicker += t.price * t.quantity;
        totalBuyQuantity += t.quantity;
      }
    });

    const avgCostPerShare =
      totalBuyQuantity > 0 ? totalCostBasisForTicker / totalBuyQuantity : 0;
    const costBasisInOriginalCurrency = holding.quantity * avgCostPerShare;

    if (process.env.NODE_ENV === "development") {
      console.log("costBasisInOriginalCurrency", costBasisInOriginalCurrency);
    }

    const convertedCostBasis = convertAmount(
      costBasisInOriginalCurrency,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );
    basePortfolioValue += convertedCostBasis;

    if (process.env.NODE_ENV === "development") {
      console.log(`  📊 ${ticker}:`);
      console.log(`    💼 Current Quantity: ${holding.quantity}`);
      console.log(
        `    💰 Total Cost Basis: ${totalCostBasisForTicker} ${assetCurrency}`
      );
      console.log(`    📦 Total Buy Quantity: ${totalBuyQuantity}`);
      console.log(
        `    💵 Avg Cost Per Share: ${avgCostPerShare} ${assetCurrency}`
      );
      console.log(
        `    💎 Cost Basis (Original): ${costBasisInOriginalCurrency} ${assetCurrency}`
      );
      console.log(
        `    💎 Cost Basis (Converted): ${convertedCostBasis} ${displayCurrency}`
      );
      console.log(
        `    📈 Running Base Value: ${basePortfolioValue} ${displayCurrency}`
      );
    }
  });

  if (process.env.NODE_ENV === "development") {
    console.log(
      `✅ Total Base Portfolio Value: ${basePortfolioValue} ${displayCurrency}\n`
    );
  }

  // Find the earliest transaction date to determine when user started investing
  const earliestTransactionDate =
    transactions.length > 0
      ? transactions.reduce(
          (earliest, t) =>
            t.transaction_date < earliest ? t.transaction_date : earliest,
          transactions[0].transaction_date
        )
      : null;

  for (const date of dates) {
    const holdings = calculatePortfolioHoldingsAtDate(transactions, date);
    let totalValue = 0;
    let hasValidPrices = false;

    // Check if this date is before the user's first transaction
    const isBeforeFirstTransaction =
      earliestTransactionDate && date < earliestTransactionDate;

    // Detailed logging for specific date
    const isTargetDate = date === "2025-08-07";
    if (isTargetDate && process.env.NODE_ENV === "development") {
      // Enable detailed currency conversion logging
      (global as any).logCurrencyConversion = true;

      console.log(`\n🔍 DETAILED CALCULATION FOR ${date}:`);
      console.log(`📊 Time Period: ${timePeriod}`);
      console.log(`💰 Display Currency: ${displayCurrency}`);
      console.log(`📅 Earliest Transaction Date: ${earliestTransactionDate}`);
      console.log(
        `⏰ Is Before First Transaction: ${isBeforeFirstTransaction}`
      );
      console.log(`🏦 Base Portfolio Value: ${basePortfolioValue}`);
      console.log(`📈 Holdings at this date:`, holdings);
      console.log(
        `💱 Available Exchange Rates:`,
        Array.from(exchangeRates.entries())
      );
    }

    if (isBeforeFirstTransaction) {
      // Use base portfolio value for dates before first transaction
      totalValue = basePortfolioValue;
      hasValidPrices = true;

      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(`✅ Using base portfolio value: ${totalValue}`);
      }
    } else {
      // Calculate portfolio value using corrected approach:
      // Portfolio Value = Base Portfolio Value + (Market Value of Holdings - Cost Basis of Holdings)
      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(`🧮 Calculating corrected portfolio value:`);
        console.log(
          `📊 Formula: Base Portfolio Value + (Market Value - Cost Basis of Holdings on Date)`
        );
      }

      let marketValueOfHoldings = 0;
      let costBasisOfHoldings = 0;
      let hasValidData = false;

      holdings.forEach((holding, ticker) => {
        const assetPrices = historicalPrices.get(ticker);
        let priceOnDate = assetPrices?.get(date);
        let priceSource = "exact";

        // If no price for exact date, try to find the closest previous price
        if (!priceOnDate && assetPrices) {
          const availableDates = Array.from(assetPrices.keys()).sort();
          const targetDate = new Date(date);

          for (let i = availableDates.length - 1; i >= 0; i--) {
            const availableDate = new Date(availableDates[i]);
            if (availableDate <= targetDate) {
              priceOnDate = assetPrices.get(availableDates[i]);
              priceSource = `fallback from ${availableDates[i]}`;
              break;
            }
          }
        }

        if (priceOnDate && holding.quantity > 0) {
          const asset = assetData.get(ticker);
          const assetCurrency = asset?.currency?.code || "EUR";

          // Calculate market value in original currency
          const marketValue = holding.quantity * priceOnDate;

          // Calculate cost basis for holdings on this date (average cost per share * quantity)
          const avgCostPerShare = holding.costBasis / holding.quantity;
          const costBasisForHolding = holding.quantity * avgCostPerShare;

          // Convert both to display currency
          const convertedMarketValue = convertAmount(
            marketValue,
            assetCurrency,
            displayCurrency,
            exchangeRates
          );

          const convertedCostBasis = convertAmount(
            costBasisForHolding,
            assetCurrency,
            displayCurrency,
            exchangeRates
          );

          marketValueOfHoldings += convertedMarketValue;
          costBasisOfHoldings += convertedCostBasis;
          hasValidData = true;

          if (isTargetDate && process.env.NODE_ENV === "development") {
            console.log(`  📊 ${ticker}:`);
            console.log(`    💼 Quantity: ${holding.quantity}`);
            console.log(
              `    💵 Price (${priceSource}): ${priceOnDate} ${assetCurrency}`
            );
            console.log(`    💰 Market Value: ${marketValue} ${assetCurrency}`);
            console.log(
              `    💰 Cost Basis: ${costBasisForHolding} ${assetCurrency}`
            );
            console.log(
              `    🔄 Exchange Rate: ${assetCurrency} -> ${displayCurrency}`
            );
            console.log(
              `    💎 Converted Market Value: ${convertedMarketValue} ${displayCurrency}`
            );
            console.log(
              `    � Converted Cost Basis: ${convertedCostBasis} ${displayCurrency}`
            );
            console.log(
              `    �📈 Running Market Value: ${marketValueOfHoldings} ${displayCurrency}`
            );
            console.log(
              `    📈 Running Cost Basis: ${costBasisOfHoldings} ${displayCurrency}`
            );
          }
        } else {
          if (isTargetDate && process.env.NODE_ENV === "development") {
            console.log(`  ❌ ${ticker}: No valid price or zero quantity`);
            console.log(`    💼 Quantity: ${holding.quantity}`);
            console.log(`    💵 Price: ${priceOnDate || "N/A"}`);
          }
        }
      });

      if (hasValidData) {
        // Apply the corrected formula: Base Portfolio Value + (Market Value - Cost Basis of Holdings)
        const gainLoss = marketValueOfHoldings - costBasisOfHoldings;
        totalValue = basePortfolioValue + gainLoss;
        hasValidPrices = true;

        if (isTargetDate && process.env.NODE_ENV === "development") {
          console.log(`\n🧮 CORRECTED CALCULATION BREAKDOWN:`);
          console.log(
            `    🏦 Base Portfolio Value: ${basePortfolioValue} ${displayCurrency}`
          );
          console.log(
            `    📈 Market Value of Holdings: ${marketValueOfHoldings} ${displayCurrency}`
          );
          console.log(
            `    💰 Cost Basis of Holdings: ${costBasisOfHoldings} ${displayCurrency}`
          );
          console.log(`    📊 Gain/Loss: ${gainLoss} ${displayCurrency}`);
          console.log(
            `    ✅ Final Portfolio Value: ${totalValue} ${displayCurrency}`
          );
        }
      }
    }

    // Add data point if we have valid data (either historical prices or base value)
    if (hasValidPrices) {
      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(
          `✅ Final Portfolio Value for ${date}: ${totalValue} ${displayCurrency}`
        );
        console.log(`📊 Adding data point to performance data\n`);

        // Disable detailed currency conversion logging
        (global as any).logCurrencyConversion = false;
      }

      performanceData.push({
        date,
        value: totalValue,
        profitLoss: 0, // Will be calculated later based on period start
        profitLossPercentage: 0, // Will be calculated later based on period start
      });
    } else {
      if (isTargetDate && process.env.NODE_ENV === "development") {
        console.log(`❌ No valid data for ${date} - skipping data point\n`);

        // Disable detailed currency conversion logging
        (global as any).logCurrencyConversion = false;
      }
    }
  }

  // Calculate current portfolio value using the same logic as composition chart
  // This ensures consistency regardless of time period selection
  let currentValue = 0;

  currentHoldings.forEach((holding, ticker) => {
    const latestPrice = latestPrices.get(ticker) || 0;
    const asset = assetData.get(ticker);
    const assetCurrency = asset?.currency?.code || "EUR";

    // Calculate current market value in original currency
    const originalValue = holding.quantity * latestPrice;

    // Convert to display currency
    const convertedValue = convertAmount(
      originalValue,
      assetCurrency,
      displayCurrency,
      exchangeRates
    );

    currentValue += convertedValue;
  });

  // Calculate time-period-specific profit/loss
  // Get portfolio value at the start of the selected time period
  let portfolioValueAtStart = 0;

  if (performanceData.length > 0) {
    // Use the first data point as the starting value for this time period
    portfolioValueAtStart = performanceData[0].value;
  } else {
    // Fallback to base portfolio value if no historical data
    portfolioValueAtStart = basePortfolioValue;
  }

  // Update all performance data points with correct profit/loss relative to period start
  const updatedPerformanceData = performanceData.map((point) => ({
    ...point,
    profitLoss: point.value - portfolioValueAtStart,
    profitLossPercentage:
      portfolioValueAtStart > 0
        ? ((point.value - portfolioValueAtStart) / portfolioValueAtStart) * 100
        : 0,
  }));

  const totalProfitLoss = currentValue - portfolioValueAtStart;
  const totalProfitLossPercentage =
    portfolioValueAtStart > 0
      ? (totalProfitLoss / portfolioValueAtStart) * 100
      : 0;

  return {
    data: updatedPerformanceData,
    currentValue,
    basePortfolioValue,
    totalProfitLoss,
    totalProfitLossPercentage,
    displayCurrency,
    timePeriod,
  };
}

/**
 * Fetch portfolio performance data for selected portfolios
 */
export async function getPortfolioPerformanceData(
  portfolioIds: string[],
  timePeriod: TimePeriod,
  displayCurrency: SupportedCurrency = "EUR"
): Promise<PortfolioPerformanceData> {
  try {
    if (portfolioIds.length === 0) {
      return {
        data: [],
        currentValue: 0,
        basePortfolioValue: 0,
        totalProfitLoss: 0,
        totalProfitLossPercentage: 0,
        displayCurrency,
        timePeriod,
      };
    }

    // Fetch transactions for selected portfolios
    const transactionsResult = await hasuraQuery<{
      ptvuser_transactions: TransactionWithAssetDetails[];
    }>(GET_PORTFOLIO_COMPOSITION_DATA, {
      variables: { portfolioIds },
    });

    const transactions = transactionsResult.ptvuser_transactions || [];
    if (transactions.length === 0) {
      return {
        data: [],
        currentValue: 0,
        basePortfolioValue: 0,
        totalProfitLoss: 0,
        totalProfitLossPercentage: 0,
        displayCurrency,
        timePeriod,
      };
    }

    // Get unique tickers
    const uniqueTickers = Array.from(
      new Set(transactions.map((t) => t.ticker))
    );

    // Fetch asset data with references
    const assetsResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        asset_id: number;
        ticker: string;
        name: string;
        company: string;
        sector?: { name: string };
        industry?: { name: string };
        currency?: { code: string; name: string; symbol: string };
        country?: { code: string; name: string };
        asset_type?: { name: string };
      }>;
    }>(GET_ASSETS_WITH_REFERENCES, { variables: { tickers: uniqueTickers } });

    const assetData = new Map<string, TransactionWithAssetDetails["asset"]>();
    assetsResult.ptvuser_asset?.forEach((asset) => {
      assetData.set(asset.ticker, asset);
    });

    // Fetch latest prices for current value calculation (same as composition chart)
    const latestPricesResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        ticker: string;
        latest_price: Array<{ close_price: number; date: string }>;
      }>;
    }>(GET_LATEST_ASSET_PRICES, {
      variables: {
        tickers: uniqueTickers,
        today: format(new Date(), "yyyy-MM-dd"),
      },
    });

    const latestPrices = new Map<string, number>();
    latestPricesResult.ptvuser_asset?.forEach((asset) => {
      if (asset.latest_price?.[0]?.close_price) {
        latestPrices.set(asset.ticker, asset.latest_price[0].close_price);
      }
    });

    // Calculate date range and fetch historical prices (always daily)
    const { startDate, endDate } = calculateDateRange(timePeriod, transactions);

    const historicalPricesResult = await hasuraQuery<{
      ptvuser_asset: Array<{
        ticker: string;
        historical_prices: Array<{ close_price: number; date: string }>;
      }>;
    }>(GET_HISTORICAL_ASSET_PRICES, {
      variables: { tickers: uniqueTickers, startDate, endDate },
    });

    // Process historical prices into the required format
    const historicalPrices = new Map<string, Map<string, number>>();
    historicalPricesResult.ptvuser_asset?.forEach((asset) => {
      const priceMap = new Map<string, number>();

      // Process daily historical prices
      asset.historical_prices?.forEach(
        (price: { close_price: number; date: string }) => {
          priceMap.set(price.date, price.close_price);
        }
      );

      historicalPrices.set(asset.ticker, priceMap);
    });

    // Get portfolio currencies and fetch exchange rates if needed
    const portfolioCurrencies = extractPortfolioCurrencies(assetData);
    const requiredExchangeRates = getRequiredExchangeRates(
      portfolioCurrencies,
      displayCurrency
    );

    let exchangeRates = new Map<string, number>();
    if (requiredExchangeRates.length > 0) {
      try {
        exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);

        // Check if we have all required exchange rates
        const existingRates = await checkExchangeRateAssetsExist(
          requiredExchangeRates
        );
        const missingRates = requiredExchangeRates.filter(
          (rate) => !existingRates.has(rate)
        );

        if (missingRates.length > 0) {
          await fetchMissingExchangeRates(missingRates);
          exchangeRates = await fetchExchangeRatesFromDB(requiredExchangeRates);
        }
      } catch (error) {
        console.error("Error fetching exchange rates:", error);
        // Continue without currency conversion if exchange rates fail
      }
    }

    return calculatePortfolioPerformance(
      transactions,
      historicalPrices,
      latestPrices,
      assetData,
      timePeriod,
      displayCurrency,
      exchangeRates
    );
  } catch (error) {
    console.error("Error fetching portfolio performance data:", error);
    throw new Error(
      "Nu s-au putut încărca datele de performanță ale portofoliului"
    );
  }
}
